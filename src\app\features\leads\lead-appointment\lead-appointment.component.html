<div class="h-100vh">
    <div class="bg-coal align-center flex-between w-100 px-20 py-12 text-white">
        <div class="align-center">
            <div class="icon ic-circle-chevron-left ic-large cursor-pointer mr-10" (click)="closeAppointmentModal()">
            </div>
            <h3>{{ 'LEADS.appointment' | translate }} {{ 'GLOBAL.details' | translate }}</h3>
        </div>
        <div>
            {{'SIDEBAR.project' | translate}} <span class="text-xl fw-600">{{data?.projects?.indexOf(selectedProject) +
                1}}</span>/{{data?.projects?.length}}
        </div>
    </div>
    <div
        [ngClass]="{'pe-none blinking': appointmentsIsLoading || activeLeadIsLoading || projectListIsLaoding || propertyListIsLoading}">
        <div class="p-20">
            <div class="bg-secondary mb-16 p-16 br-4 text-mud text-sm ip-w-100-40">
                <h4 class="fw-600 text-coal text-truncate-1 break-all max-w-460 ip-max-w-200">
                    {{ data?.name }}
                </h4>
                <div class="align-center mt-8">
                    <span class="align-center">
                        <span class="icon ic-Call mr-6 ic-slate-90 ic-xxxs"></span>
                        <span>{{data?.contactNo ? data?.contactNo : "---" }}</span>
                        <!-- <a [href]="data.contactNo ? 'tel:' + data.contactNo : ''">{{
                            data?.contactNo
                            }}</a> -->
                    </span>
                    <span class="align-center">
                        <span class="icon ic-Call ic-slate-90 ic-xxxs mr-6 ml-20"></span>
                        <span>{{data?.alternateContactNo ? data?.alternateContactNo : "---" }}</span>
                        <!-- <a [ngClass]="{ 'pe-none': !data.alternateContactNo }"
                            [href]="'tel:' + data.alternateContactNo">{{
                            data?.alternateContactNo ? data?.alternateContactNo : "---" }}</a> -->
                    </span>
                    <span class="align-center text-truncate">
                        <span class="icon ic-mail ic-slate-90 ic-xxs mr-6 ml-20"></span>
                        <span>{{data?.email ? data?.email : "---" }}</span>
                        <!-- <a [href]="'mailto:' + data.email" [ngClass]="{ 'pe-none': !data.email }">
                            {{ data?.email ? data.email : "---" }}</a> -->
                    </span>
                </div>
                <div class="align-center mt-8">
                    <span class="icon ic-location-circle ic-slate-90 ic-xxs mr-6"></span>
                    <drag-scroll class="scrollbar scroll-hide">
                        <div class="text-truncate-1 break-all">{{addresses}}
                        </div>
                    </drag-scroll>
                </div>
                <div class="align-center ip-w-100-70 text-nowrap scrollbar scroll-hide mt-8">
                    <span class="icon ic-apartment ic-slate-90 ic-xxs mr-6"></span>
                    <span class="align-center"
                        *ngFor="let enquiry of data?.enquiry?.enquiryTypes || ['']; let last = last">
                        <span class="text-accent-green text-sm fw-600">{{enquiry
                            ? EnquiryType[enquiry] : "--"}}</span>
                        <span *ngIf="!last" class="dot dot-xxs bg-accent-green mx-6"></span>
                    </span>
                    <ng-container *ngIf="data?.enquiry?.propertyTypes?.[0]?.displayName">
                        <span class="dot dot-xxs bg-dark-700 mx-6"></span>
                        <ng-container *ngIf="data?.enquiry?.propertyTypes?.length; else noSubtype">
                            <ng-container *ngFor="let type of data?.enquiry?.propertyTypes">
                                <span>{{ type.childType.displayName }}</span>
                            </ng-container>
                        </ng-container>
                        <ng-template #noSubtype>
                            <h6>--</h6>
                        </ng-template>
                    </ng-container>
                    <ng-container *ngIf="data?.enquiry?.propertyTypes?.length">
                        <span class="dot dot-xxs bg-dark-700 mx-6"></span>
                        <ng-container *ngIf="data?.enquiry?.propertyTypes?.length; else noSubtype">
                            <ng-container *ngFor="let type of data?.enquiry?.propertyTypes">
                                <span *ngIf="type?.childType?.displayName">
                                    {{ type.childType.displayName }}
                                </span>
                            </ng-container>
                        </ng-container>
                        <ng-template #noSubtype>
                            <h6>--</h6>
                        </ng-template>
                    </ng-container>
                    <ng-container *ngFor="let noOfBHK of data?.enquiry?.bhKs">
                        <span class="dot dot-xxs bg-dark-700 mx-6"></span>
                        <span>{{ globalSettingsData?.isCustomLeadFormEnabled ? getBRDisplayString(noOfBHK) :
                            getBHKDisplayString(noOfBHK) }}</span>
                    </ng-container>
                    <ng-container *ngFor="let bhkType of data?.enquiry?.bhkTypes">
                        <span class="dot dot-xxs bg-dark-700 mx-6"></span>
                        <span>{{ BHKType[bhkType] }}</span>
                    </ng-container>
                    <ng-container *ngIf="data?.enquiry?.lowerBudget || data?.enquiry?.upperBudget">
                        <span class="dot dot-xxs bg-dark-700 mx-6"></span>
                        <span>{{ formatBudget(data?.enquiry?.lowerBudget,data?.enquiry?.currency || defaultCurrency) }}
                            <span *ngIf="data?.enquiry?.lowerBudget && data?.enquiry?.upperBudget">-</span>
                            {{ formatBudget(data?.enquiry?.upperBudget,data?.enquiry?.currency || defaultCurrency)
                            }}</span>
                    </ng-container>
                </div>
            </div>
            <div class="bg-secondary br-4 p-10 fw-300 flex-between"
                [ngClass]="{'pe-none blinking': isMeetingOrVisitDoneLoading}" *ngIf="!canShowAddProject">
                <drag-scroll class="scrollbar scroll-hide" [ngClass]="!canShowDoneForm ? 'w-100-150' : 'w-100-60'">
                    <div class="flex-nowrap text-nowrap align-center">
                        <span *ngIf="!data?.projects?.length" class="text-red-350">No Projetcs</span>
                        <span *ngFor="let project of data?.projects" [id]="project?.id" class="mr-16 cursor-pointer"
                            [ngClass]="[appointments?.[project?.name] ? 'text-accent-green' : appointments?.[project?.name] == false ? 'text-red-350' : 'text-mud', selectedProject == project ? 'selected-project text-decoration-underline': '']"
                            (click)="selectedProjectChanged(project)">
                            <span>{{project?.name}}
                                <span *ngIf="appointments?.[project?.name] == false"
                                    class="icon ic-close ic-red-350 ic-xxs ml-4"></span>
                                <span *ngIf="appointments?.[project?.name]"
                                    class="icon ic-circle-check ic-accent-green ic-xxs ml-4"></span>
                            </span>
                        </span>
                    </div>
                </drag-scroll>
                <div class="flex-center ml-6 cursor-pointer" *ngIf="!canShowDoneForm"
                    (click)="canShowAddProject = true; filterProjects();">
                    <span class="mr-6 icon ic-plus ic-xxs ic-accent-green"></span>
                    <span class="text-accent-green text-nowrap">{{'SIDEBAR.add' | translate}} {{'SIDEBAR.project' |
                        translate}}</span>
                </div>
            </div>
        </div>
        <div class="align-center-col flex-center scrollbar h-100-354"
            [ngClass]="{'pe-none blinking': isMeetingOrVisitDoneLoading}"
            *ngIf="!canShowDoneForm && !canShowAddProject">
            <img src="../../../../assets/gifs/muso-walk.gif" alt="Muso walking" class="max-h-90pr">
            <span class="text-xl fw-600 text-coal">
                <!-- is {{
                data?.status?.shouldOpenAppointmentPage
                ? (data?.status?.shouldUseForMeeting ? 'meeting' : 'site visit')
                : ''
                }} done? -->
                is {{(isCustomStatusEnabled ? (data?.status?.shouldUseForMeeting ||
                data?.status?.childType?.shouldUseForMeeting) : data?.status?.status === 'meeting_scheduled') ? 'meeting
                done' : globalSettingsData?.shouldRenameSiteVisitColumn ? 'referral taken' : 'site visit done'}}?
            </span>
        </div>
        <div *ngIf="canShowAddProject" [ngClass]="{'pe-none blinking': addProjectsIsLoading}" class="px-20">
            <div for="project" class="field-label mt-0">add project name</div>
            <ng-select [virtualScroll]="true" [items]="projectsList" [multiple]="true" [closeOnSelect]="false"
                ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="projectToAdd">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                            class="checkmark"></span>{{item}}
                    </div>
                </ng-template>
            </ng-select>
            <div class="flex-end mt-20">
                <div class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer"
                    id="btnCancelUpdateStatus" data-automate-id="btnCancelUpdateStatus"
                    (click)="canShowAddProject = false; projectToAdd = null">{{'BUTTONS.cancel' |
                    translate}}</div>
                <div class="mx-12 border-left h-16"></div>
                <button class="btn-coal" id="btnCancelUpdateStatus" data-automate-id="btnCancelUpdateStatus"
                    (click)="addProject()">{{'SIDEBAR.add' | translate}}</button>
            </div>
        </div>
        <div class="mt-16 w-100 px-20 d-flex" [ngClass]="{'pe-none blinking': isMeetingOrVisitDoneLoading}"
            *ngIf="!canShowDoneForm && !canShowAddProject">
            <div class="w-50 br-50px border-accent-green fw-700 text-large px-10 py-8 flex-center text-accent-green mx-6 cursor-pointer"
                (click)="canShowDoneForm = true">{{data?.status?.displayName === 'Meeting Scheduled' ? 'Done' :
                globalSettingsData?.shouldRenameSiteVisitColumn ? 'Taken' : 'Done'}}</div>
            <div class="w-50 br-50px border-red fw-700 text-large px-10 py-8 flex-center text-red mx-6 cursor-pointer"
                (click)="updateNotDone(false)">{{data?.status?.displayName === 'Meeting Scheduled' ? 'Not Done' :
                globalSettingsData?.shouldRenameSiteVisitColumn ? 'Not Taken' : 'Not Done'}}</div>
        </div>
        <div class="flex-center mt-16 pt-10 border-top cursor-pointer"
            [ngClass]="{'pe-none blinking': isMeetingOrVisitDoneLoading}" *ngIf="!canShowDoneForm && !canShowAddProject"
            (click)="selectNextProject()">
            <span class="icon ic-clock-rotate-left ic-slate-90 ic-sm  mirror-element mr-10"></span>
            <span class="fw-semi-bold text-large text-black-100">Update later</span>
        </div>
        <!-- Meeting or site visit done form -->
        <div class="px-20" [ngClass]="{'pe-none blinking': isMeetingOrVisitDoneLoading}" *ngIf="canShowDoneForm">
            <div class="flex-between header-5">
                <span class="fw-600 text-coal">is {{(isCustomStatusEnabled ? (data?.status?.shouldUseForMeeting ||
                    data?.status?.childType?.shouldUseForMeeting) : data?.status?.status === 'meeting_scheduled') ?
                    'meeting done' : globalSettingsData?.shouldRenameSiteVisitColumn ? 'referral taken' : 'site visit
                    done'}}</span>
                <div class="cursor-pointer" (click)="canShowDoneForm = false">
                    <span class="fw-semi-bold text-accent-green text-decoration-underline mr-8">
                        {{(isCustomStatusEnabled ? (data?.status?.shouldUseForMeeting ||
                        data?.status?.childType?.shouldUseForMeeting) : data?.status?.status === 'meeting_scheduled') ?
                        'done' :
                        globalSettingsData?.shouldRenameSiteVisitColumn ? 'taken' : 'done'}}</span>
                    <span class="icon ic-pencil ic-xxs ic-accent-green"></span>
                </div>
            </div>
            <form [formGroup]="visitMeetingDetailsForm" autocomplete="off" class="scrollbar h-100-333 pr-10">
                <div
                    [ngClass]="((data?.status?.displayName === 'Meeting Scheduled' || data?.status?.displayName === 'Referral Scheduled') && isProjectsMandatorymeeting)
                || ((data?.status?.displayName === 'Site Visit Scheduled' || data?.status?.displayName === 'Referral Scheduled') && isProjectsMandatorySiteVisit) ? 'field-label-req': 'field-label'">
                    Change
                    Project</div>
                <div class="dashboard-dropdown">
                    <form-errors-wrapper [control]="visitMeetingDetailsForm.controls['project']" label="Project">
                        <ng-select [virtualScroll]="true" [items]="projectsList" formControlName="project"
                            ResizableDropdown placeholder="Select"></ng-select>
                    </form-errors-wrapper>
                </div>
                <!-- <div [ngClass]="(data?.status?.displayName === 'Meeting Scheduled' && isPropertyMandatorymeeting)
                || (data?.status?.displayName === 'Site Visit Scheduled' && isPropertyMandatorySiteVisit) ? 'field-label-req': 'field-label'">change property</div>
                <ng-select [virtualScroll]="true" [items]="propertyList" formControlName="property" placeholder="Select"></ng-select> -->
                <div class="field-label">{{ data?.status?.displayName === 'Meeting Scheduled' ? 'Meeting Done By' :
                    globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Taken By' : 'Site Visit Done By'
                    }}</div>

                <form-errors-wrapper [control]="visitMeetingDetailsForm.controls['userId']"
                    label="{{ data?.status?.displayName === 'Meeting Scheduled' ? 'Meeting Done By' : globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Taken By' : 'Site Visit Done By' }}">
                    <div class="dashboard-dropdown">

                        <ng-select [virtualScroll]="true" formControlName="userId" ResizableDropdown
                            placeholder="Select user" (change)="onUserSelectionChange($event)">
                            <ng-option *ngFor="let user of activeUsers" [value]="user.id" class="bg-red">
                                {{user.firstName}} {{user.lastName}}
                            </ng-option>
                        </ng-select>
                    </div>

                </form-errors-wrapper>
                <div class="field-label">Appointment Done On</div>
                <form-errors-wrapper [control]="visitMeetingDetailsForm.controls['appointmentDoneOn']" label="Appointment Done On">
                    <input [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1"
                        [min]="!isPastDateSelectionEnabled ? minDate : null" readonly id="inpAppDateTime"
                        data-automate-id="inpAppDateTime" formControlName="appointmentDoneOn"
                        placeholder="ex. 05/03/2025, 9:20 am">
                    <owl-date-time #dt1 [hour12Timer]="'true'" [startAt]="minDate"
                        (afterPickerOpen)="onPickerOpened(minDate)"></owl-date-time>
                </form-errors-wrapper>
                <div class="field-label">{{"LOCATION.location" | translate}}</div>
                <!-- <ng-container *ngIf="isManualLocation else autoLocation">
                    <div class="position-relative field-tag">
                        <ng-select [virtualScroll]="true" formControlName="location" (search)="searchPlaceTerm$.next($event.term)"
                            placeholder="ex. Hsr Layout" class="bg-white">
                            <ng-option *ngFor="let places of placesList" [value]="places.placeId">
                                {{places.localityDisplayText}},{{places.localitySubtitleText}}</ng-option>
                        </ng-select>
                        <div class="search icon ic-search ic-sm ic-coal"></div>
                    </div>
                    <span class="cursor-pointer mt-12 align-center" (click)="isManualLocation = false">
                        <span class="icon ic-add ic-accent-green ic-sm"></span>
                        <span class="text-sm text-accent-green fw-600 ml-6 mb-2">go to current location</span>
                    </span>
                </ng-container>
                <ng-template #autoLocation>
                    <div class="form-group">
                        <input type="text" readonly disabled [value]="currentLocation" />
                    </div>
                    <span class="cursor-pointer mt-12 align-center" (click)="isManualLocation = true">
                        <span class="icon ic-add ic-accent-green ic-sm"></span>
                        <span class="text-sm text-accent-green fw-600 ml-6 mb-2">add location manually</span>
                    </span>
                    <span *ngIf="!currentLocation" class="text-xs text-red fw-semi-bold">Location Permission Turned Off</span>
                </ng-template> -->
                <div [ngClass]="locationCategory?.length !== 2 ? 'flex-between' : 'd-flex'"
                    class="ph-flex-col ph-flex-start">
                    <div *ngFor="let lType of locationCategory; let i = index">
                        <div class="form-check form-check-inline align-center btn pl-2 mr-0">
                            <input type="radio" id="inpLocation{{ i }}" data-automate-id="inpLocation{{ i }}"
                                name="location" [checked]="lType.value === selectedLocationType" [value]="lType.value"
                                class="radio-check-input mr-10" (change)="selectedLocationType = lType.value" />
                            <label class="fw-600 text-secondary cursor-pointer text-large" for="inpLocation{{ i }}">
                                {{ lType.dispName }}</label>
                        </div>
                    </div>
                </div>
                <ng-container *ngIf="selectedLocationType == 'search'">
                    <div class="field-label">{{"LOCATION.location" | translate}}</div>
                    <div class="field-tag">
                        <ng-select [virtualScroll]="true" formControlName="location"
                            (search)="searchPlaceTerm$.next($event.term)" [editableSearchTerm]="true" ResizableDropdown
                            placeholder="Select Location" class="bg-white" [items]="placesList" bindLabel="location">
                            <ng-template ng-option-tmp let-item="item">
                                <div title="{{item?.location}}">{{item?.location}}</div>
                            </ng-template>
                        </ng-select>
                        <div class="search icon ic-search ic-sm ic-coal"></div>
                    </div>
                </ng-container>
                <ng-container *ngIf="selectedLocationType == 'manual'">
                    <div class="flex-between w-100 flex-wrap">
                        <div class="form-group pr-10 w-50 ph-w-100 ph-mr-0">
                            <label class="field-label">Country</label>
                            <input type="text" formControlName="country" placeholder="ex. India">
                        </div>
                        <div class="form-group w-50 ph-w-100">
                            <div class="field-label">State</div>
                            <input type="text" formControlName="state" placeholder="ex. Karnataka">
                        </div>
                        <div class="form-group w-50 pr-10 ph-w-100 ph-mr-0">
                            <label class="field-label">City</label>
                            <input type="text" formControlName="city" placeholder="ex. Bengaluru">
                        </div>
                        <div class="form-group w-50 ph-w-100 ph-mr-0">
                            <label class="field-label">Locality</label>
                            <input type="text" formControlName="locality" placeholder="ex. Hsr Layout">
                        </div>
                    </div>
                </ng-container>

                <div class="mt-16 w-100">
                    <div class="flex-between">
                        <div class="field-label mt-0">Add Documents</div>
                        <div class="flex-center btn btn-accent-green btn-md w-170 text-sm clear-padding-x"
                            *ngIf="!isUpload" (click)="isUpload = true"><span
                                class="icon ic-cloud-upload ic-xxs mr-8"></span>
                            {{ (uploadedFiles?.length ? 'LEADS.upload-another-document' : 'LEADS.upload-new-document') |
                            translate }}
                        </div>
                    </div>
                    <div *ngIf="isUpload">
                        <form class="mb-16" [formGroup]="documentsForm" autocomplete="off">
                            <div class="form-group">
                                <div class="field-label">{{'LEADS.document-title' | translate }}</div>
                                <input type="text" formControlName="docTitle" id="inpDocTitle"
                                    data-automate-id="inpDocTitle" placeholder="ex. Title">
                            </div>
                        </form>
                        <div class="br-6 p-10 w-100 bg-accent-green-light mt-16 custom-flex-row">
                            <browse-drop-upload [allowedFileType]="'imgPdfDoc'"
                                [allowedFileFormat]="fileFormatToBeUploaded" (uploadedFile)="onFileSelection($event)"
                                (uploadedFileName)="selectedFileName = $event"></browse-drop-upload>
                        </div>
                        <div class="flex-end mt-10">
                            <div class="btn-gray mr-10" (click)="isUpload = false">{{ 'BUTTONS.cancel' | translate
                                }}</div>
                            <div class="btn-coal" (click)="addDocument()">{{ 'SIDEBAR.add' | translate }}
                                Document</div>
                        </div>
                    </div>
                    <ng-container *ngIf="uploadedFiles?.length else noDocuments">
                        <div class="d-flex flex-wrap tb-w-100 ip-flex-center my-12">
                            <div *ngFor="let doc of uploadedFiles; let i = index" class="flex-col w-100">
                                <div class="p-4 flex-between bg-white br-6 mb-10 w-100">
                                    <div class="align-center">
                                        <span class="icon ic-file ic-sm ic-black"></span>
                                        <div class="text-truncate-1 break-all fw-600 ml-8">{{uploadedFilesName[i]}}
                                        </div>
                                    </div>
                                    <div class="align-center">
                                        <a class="icon ic-delete ic-red ic-sm cursor-pointer ml-10"
                                            id="clkDeleteLeadDoc" data-automate-id="clkDeleteLeadDoc"
                                            (click)="onClickRemoveDocument(i)"></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
                <div
                    [ngClass]="(data?.status?.displayName === 'Meeting Scheduled' && isNotesMandatoryMeeting)
                        || ((data?.status?.displayName === 'Site Visit Scheduled' || data?.status?.displayName === 'Referral Scheduled') && isNotesMandatorySiteVisit) ? 'field-label-req': 'field-label'">
                    {{'TASK.notes' | translate}}</div>
                <form-errors-wrapper [control]="visitMeetingDetailsForm.controls['notes']"
                    label="{{'TASK.notes' | translate}}">
                    <textarea rows="3" formControlName="notes" placeholder="ex. I want to say "></textarea>
                </form-errors-wrapper>
            </form>
            <div class="flex-end align-center mt-20">
                <h5 class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer"
                    id="btnCancelUpdateStatus" data-automate-id="btnCancelUpdateStatus"
                    (click)="selectedProjectChanged(selectedProject)">
                    {{'BUTTONS.cancel' | translate}}</h5>
                <div class="mx-12 border-left h-16"></div>
                <h5 class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer"
                    id="btnCancelUpdateStatus" data-automate-id="btnCancelUpdateStatus"
                    (click)="fileUploadToS3(true, true)">Save & Close</h5>
                <div class="mx-12 border-left h-16"></div>
                <button class="btn-coal text-nowrap w-min-content px-8"
                    (click)="fileUploadToS3(true, false, data?.projects?.indexOf(selectedProject) == data?.projects?.length - 1)"
                    [ngClass]="data?.projects?.indexOf(selectedProject) < data?.projects?.length - 1 || (
                        (data?.projects?.indexOf(selectedProject) == data?.projects?.length - 1)
                        && (((appointments | keyvalue)?.length) == data?.projects?.length) || ((appointments | keyvalue)?.length == data?.projects?.length-1 && appointments?.[selectedProject?.name] == undefined)) ? '' : 'pe-none bg-slate-250'">
                    {{ data?.projects?.indexOf(selectedProject) == data?.projects?.length - 1 ?
                    'Save & Update Status' : 'Save & Next'}}</button>
            </div>
        </div>
    </div>